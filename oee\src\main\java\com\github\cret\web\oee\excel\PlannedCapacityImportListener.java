package com.github.cret.web.oee.excel;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.domain.capacity.PlannedCapacityExcelDto;
import com.github.cret.web.oee.mapper.PlannedCapacityMapper;
import com.github.cret.web.oee.repository.PlannedCapacityRepository;

public class PlannedCapacityImportListener implements ReadListener<PlannedCapacityExcelDto> {

	private final PlannedCapacityRepository repository;

	private final PlannedCapacityMapper mapper;

	private final List<PlannedCapacity> successList = new ArrayList<>();

	private final List<String> failureList = new ArrayList<>();

	public PlannedCapacityImportListener(PlannedCapacityRepository repository, PlannedCapacityMapper mapper) {
		this.repository = repository;
		this.mapper = mapper;
	}

	@Override
	public void invoke(PlannedCapacityExcelDto dto, AnalysisContext context) {
		try {
			PlannedCapacity entity = mapper.toEntity(dto);
			entity.setId(String.format("%s_%s", entity.getLineCode(), entity.getProductModel()));
			successList.add(entity);
		}
		catch (Exception e) {
			// log.warn("parse excel row failed", e);
			failureList.add("第" + context.readRowHolder().getRowIndex() + "行数据解析失败:" + e.getMessage());
		}
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		if (!successList.isEmpty()) {
			repository.saveAll(successList);
		}
	}

	public List<PlannedCapacity> getSuccessList() {
		return successList;
	}

	public List<String> getFailureList() {
		return failureList;
	}

}
