package com.github.cret.web.oee.domain.capacity;

import com.alibaba.excel.annotation.ExcelProperty;

public class PlannedCapacityExcelDto {

	@ExcelProperty("线体编码")
	private String lineCode;

	@ExcelProperty("产品名称")
	private String productModel;

	@ExcelProperty("标准产能（pcs/h）")
	private Integer plannedQuantity;

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public Integer getPlannedQuantity() {
		return plannedQuantity;
	}

	public void setPlannedQuantity(Integer plannedQuantity) {
		this.plannedQuantity = plannedQuantity;
	}

}
