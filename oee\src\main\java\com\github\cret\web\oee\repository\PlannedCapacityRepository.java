package com.github.cret.web.oee.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;

import com.github.cret.web.oee.document.PlannedCapacity;

public interface PlannedCapacityRepository extends MongoRepository<PlannedCapacity, String> {

	/**
	 * 根据线体编码和产品名称获取产能
	 * @param lineCode 线体编码
	 * @param productModel 产品型号
	 * @return 计划产能的可选对象
	 */
	Optional<PlannedCapacity> findByLineCodeAndProductModel(String lineCode, String productModel);

	/**
	 * 根据线体编码和产品型号列表查询计划产能
	 * @param lineCode 线体编码
	 * @param productModels 产品型号列表
	 * @return 匹配的计划产能列表
	 */
	List<PlannedCapacity> findByLineCodeAndProductModelIn(String lineCode, List<String> productModels);

}