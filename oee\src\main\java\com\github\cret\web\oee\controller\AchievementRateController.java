package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.AchievementRate;
import com.github.cret.web.oee.service.AchievementRateService;

@RestController
@RequestMapping("/achievement-rate")
public class AchievementRateController {

	private final AchievementRateService achievementRateService;

	public AchievementRateController(AchievementRateService achievementRateService) {
		this.achievementRateService = achievementRateService;
	}

	@PostMapping
	public AchievementRate save(@RequestBody AchievementRate achievementRate) {
		return achievementRateService.save(achievementRate);
	}

	@PutMapping("/{id}")
	public AchievementRate update(@PathVariable String id, @RequestBody AchievementRate achievementRate) {
		return achievementRateService.update(id, achievementRate);
	}

	@DeleteMapping("/{id}")
	public void delete(@PathVariable String id) {
		achievementRateService.delete(id);
	}

	@GetMapping("/{id}")
	public AchievementRate findById(@PathVariable String id) {
		return achievementRateService.findById(id);
	}

	@GetMapping
	public List<AchievementRate> findAll() {
		return achievementRateService.findAll();
	}

	@PostMapping("/page")
	public PageList<AchievementRate> page(@RequestBody PageableParam<AchievementRate> param) {
		return achievementRateService.page(param);
	}

}
